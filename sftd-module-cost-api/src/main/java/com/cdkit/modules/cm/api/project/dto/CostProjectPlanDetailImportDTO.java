package com.cdkit.modules.cm.api.project.dto;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 项目计划明细Excel导入DTO
 * @Author: sunhzh
 * @Date: 2025-08-05
 * @Version: V1.0
 */
@Schema(description = "项目计划明细Excel导入DTO")
@Data
public class CostProjectPlanDetailImportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**区块编码*/
    @Excel(name = "区块编码", width = 15)
    @Schema(description = "区块编码")
    private String blockCode;

    /**区块名称*/
    @Excel(name = "区块名称", width = 15)
    @Schema(description = "区块名称")
    private String blockName;

    /**平台设施编号*/
    @Excel(name = "平台设施编号", width = 15)
    @Schema(description = "平台设施编号")
    private String platformFacilityCode;

    /**平台设施名称*/
    @Excel(name = "平台设施名称", width = 15)
    @Schema(description = "平台设施名称")
    private String platformFacilityName;

    /**产品编码*/
    @Excel(name = "产品编码", width = 15)
    @Schema(description = "产品编码")
    private String productCode;

    /**产品名称*/
    @Excel(name = "产品名称", width = 15)
    @Schema(description = "产品名称")
    private String productName;

    /**密度*/
    @Excel(name = "密度", width = 15)
    @Schema(description = "密度")
    private BigDecimal density;

    /**用量*/
    @Excel(name = "用量", width = 15)
    @Schema(description = "用量")
    private BigDecimal usageAmount;

    /**预计年处理量（油，单位方）*/
    @Excel(name = "预计年处理量（油，单位方）", width = 15)
    @Schema(description = "预计年处理量（油，单位方）")
    private BigDecimal estimatedAnnualOil;

    /**预计年处理量（水，单位方）*/
    @Excel(name = "预计年处理量（水，单位方）", width = 15)
    @Schema(description = "预计年处理量（水，单位方）")
    private BigDecimal estimatedAnnualWater;

    /**收费费率（元/方）*/
    @Excel(name = "收费费率（元/方）", width = 15)
    @Schema(description = "收费费率（元/方）")
    private BigDecimal feeRate;

    // 以下字段为后端计算字段，不在Excel中

    /**年度预算应收（油，单位万元）*/
    @Schema(description = "年度预算应收（油，单位万元）")
    private BigDecimal revenueOil;

    /**年度预算应收（水，单位万元）*/
    @Schema(description = "年度预算应收（水，单位万元）")
    private BigDecimal revenueWater;

    /**年度预算需求吨*/
    @Schema(description = "年度预算需求吨")
    private BigDecimal demandTon;

    /**
     * 根据合同模式计算年度预算应收（油）
     * 
     * @param contractMode 合同模式
     */
    public void calculateRevenueOil(String contractMode) {
        if (feeRate == null) {
            this.revenueOil = BigDecimal.ZERO;
            return;
        }

        BigDecimal baseAmount;
        if ("费率合同".equals(contractMode)) {
            // 费率合同：预计年处理量（油，方）× 收费费率（元/方）÷ 10000
            baseAmount = estimatedAnnualOil != null ? estimatedAnnualOil : BigDecimal.ZERO;
        } else {
            // 总价合同：用量 × 收费费率（元/方）÷ 10000
            baseAmount = usageAmount != null ? usageAmount : BigDecimal.ZERO;
        }

        this.revenueOil = baseAmount
                .multiply(feeRate)
                .divide(new BigDecimal("10000"), 6, java.math.RoundingMode.HALF_UP);
    }

    /**
     * 根据合同模式计算年度预算应收（水）
     * 
     * @param contractMode 合同模式
     */
    public void calculateRevenueWater(String contractMode) {
        if (feeRate == null) {
            this.revenueWater = BigDecimal.ZERO;
            return;
        }

        BigDecimal baseAmount;
        if ("费率合同".equals(contractMode)) {
            // 费率合同：预计年处理量（水，方）× 收费费率（元/方）÷ 10000
            baseAmount = estimatedAnnualWater != null ? estimatedAnnualWater : BigDecimal.ZERO;
        } else {
            // 总价合同：用量 × 收费费率（元/方）÷ 10000
            baseAmount = usageAmount != null ? usageAmount : BigDecimal.ZERO;
        }

        this.revenueWater = baseAmount
                .multiply(feeRate)
                .divide(new BigDecimal("10000"), 6, java.math.RoundingMode.HALF_UP);
    }

    /**
     * 计算年度预算需求吨
     * 年度预算需求吨 = 密度 × 用量
     */
    public void calculateDemandTon() {
        if (density != null && usageAmount != null) {
            this.demandTon = density.multiply(usageAmount)
                    .setScale(4, java.math.RoundingMode.HALF_UP);
        } else {
            this.demandTon = BigDecimal.ZERO;
        }
    }

    /**
     * 计算所有相关数据
     * 
     * @param contractMode 合同模式
     */
    public void calculateAll(String contractMode) {
        calculateRevenueOil(contractMode);
        calculateRevenueWater(contractMode);
        calculateDemandTon();
    }

    /**
     * 数据验证
     * 
     * @return 验证错误信息，如果验证通过返回null
     */
    public String validate() {
        // 必填字段检查
        if (productName == null || productName.trim().isEmpty()) {
            return "产品名称不能为空";
        }
        
        if (density == null) {
            return "密度不能为空";
        }
        
        if (usageAmount == null) {
            return "用量不能为空";
        }
        
        if (feeRate == null) {
            return "收费费率不能为空";
        }

        // 数值范围检查
        if (density.compareTo(BigDecimal.ZERO) <= 0) {
            return "密度必须大于0";
        }
        
        if (usageAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return "用量必须大于0";
        }
        
        if (feeRate.compareTo(BigDecimal.ZERO) <= 0) {
            return "收费费率必须大于0";
        }

        return null; // 验证通过
    }
}
